# AI Chat Setup Guide

## Hướng dẫn thiết lập chức năng chat AI với Gemini API

### 1. Lấy Gemini API Key

1. T<PERSON>y cập [Google AI Studio](https://makersuite.google.com/app/apikey)
2. <PERSON><PERSON><PERSON> nhập bằng tài kho<PERSON>n Google
3. Tạo API key mới
4. Sao chép API key

### 2. Cấu hình Backend

1. Mở file `backend/.env`
2. Thay thế `your_gemini_api_key_here` bằng API key thực:
   ```
   GEMINI_API_KEY=your_actual_api_key_here
   ```

### 3. Khởi động ứng dụng

1. **Backend:**
   ```bash
   cd backend
   npm run dev
   ```

2. **Frontend:**
   ```bash
   npm run dev
   ```

### 4. Sử dụng AI Chat

1. Truy cập trang Support: `http://localhost:1234/support.html`
2. Nhập câu hỏi về tái chế vào ô chat
3. <PERSON><PERSON><PERSON><PERSON> nút gửi hoặc Enter để gửi câu hỏi
4. AI sẽ trả lời dựa trên kiến thức về tái chế và bảo vệ môi trường

### 5. Tính năng

- **Rate limiting**: Giới hạn 10 requests/phút cho mỗi IP
- **Validation**: Kiểm tra độ dài câu hỏi (1-1000 ký tự)
- **Error handling**: Xử lý lỗi API và hiển thị thông báo phù hợp
- **Loading states**: Hiển thị trạng thái loading khi đang xử lý
- **Responsive design**: Giao diện thân thiện trên mọi thiết bị

### 6. API Endpoints

- `POST /api/ai/chat` - Chat với AI
- `GET /api/ai/suggested-questions` - Lấy câu hỏi gợi ý

### 7. Troubleshooting

**Lỗi "AI service chưa được cấu hình":**
- Kiểm tra API key trong file .env
- Đảm bảo API key hợp lệ

**Lỗi "Không thể kết nối đến server":**
- Kiểm tra backend server đang chạy trên port 5001
- Kiểm tra CORS configuration

**Lỗi "Quá nhiều yêu cầu":**
- Đợi 1 phút trước khi gửi request tiếp theo
- Rate limit: 10 requests/phút

### 8. Customization

Để tùy chỉnh AI responses, chỉnh sửa `SYSTEM_PROMPT` trong file `backend/controllers/aiController.js`.
