const { validationResult } = require('express-validator');
const WasteCollectionRequest = require('../models/WasteCollectionRequest');
const User = require('../models/User');
const {
  successResponse,
  errorResponse,
  notFoundResponse,
  validationErrorResponse
} = require('../utils/responseHelper');

// @desc    Tạo đơn đăng ký thu gom rác mới
// @route   POST /api/waste-collection
// @access  Private (User)
const createRequest = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return validationErrorResponse(res, errors.array());
    }

    const {
      fullName,
      phone,
      address,
      wasteTypes,
      estimatedWeight,
      preferredDate,
      preferredTime,
      notes
    } = req.body;

    // Tạo đơn đăng ký mới
    const request = await WasteCollectionRequest.create({
      user: req.user._id,
      fullName,
      phone,
      address,
      wasteTypes,
      estimatedWeight,
      preferredDate,
      preferredTime,
      notes
    });

    // Populate user info
    await request.populate('user', 'name username email');

    successResponse(res, { request }, 'Tạo đơn đăng ký thành công', 201);
  } catch (error) {
    console.error('Create waste collection request error:', error);
    errorResponse(res, 'Lỗi server khi tạo đơn đăng ký');
  }
};

// @desc    Lấy danh sách đơn đăng ký của user hiện tại
// @route   GET /api/waste-collection/my-requests
// @access  Private (User)
const getMyRequests = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const status = req.query.status;
    const query = { user: req.user._id };

    if (status && ['pending', 'approved', 'rejected', 'completed', 'cancelled'].includes(status)) {
      query.status = status;
    }

    const requests = await WasteCollectionRequest.find(query)
      .populate('reviewedBy', 'name username')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await WasteCollectionRequest.countDocuments(query);

    successResponse(res, {
      requests,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }, 'Lấy danh sách đơn đăng ký thành công');
  } catch (error) {
    console.error('Get my requests error:', error);
    errorResponse(res, 'Lỗi server khi lấy danh sách đơn đăng ký');
  }
};

// @desc    Lấy chi tiết đơn đăng ký
// @route   GET /api/waste-collection/:id
// @access  Private (User - chỉ đơn của mình, Admin - tất cả)
const getRequestById = async (req, res) => {
  try {
    const request = await WasteCollectionRequest.findById(req.params.id)
      .populate('user', 'name username email phone')
      .populate('reviewedBy', 'name username');

    if (!request) {
      return notFoundResponse(res, 'Không tìm thấy đơn đăng ký');
    }

    // Kiểm tra quyền truy cập
    if (req.user.role !== 'admin' && request.user._id.toString() !== req.user._id.toString()) {
      return errorResponse(res, 'Bạn không có quyền xem đơn đăng ký này', 403);
    }

    successResponse(res, { request }, 'Lấy thông tin đơn đăng ký thành công');
  } catch (error) {
    console.error('Get request by id error:', error);
    if (error.name === 'CastError') {
      return notFoundResponse(res, 'ID đơn đăng ký không hợp lệ');
    }
    errorResponse(res, 'Lỗi server khi lấy thông tin đơn đăng ký');
  }
};

// @desc    Cập nhật đơn đăng ký (chỉ khi status = pending)
// @route   PUT /api/waste-collection/:id
// @access  Private (User - chỉ đơn của mình)
const updateRequest = async (req, res) => {
  try {
    const request = await WasteCollectionRequest.findById(req.params.id);

    if (!request) {
      return notFoundResponse(res, 'Không tìm thấy đơn đăng ký');
    }

    // Kiểm tra quyền sở hữu
    if (request.user.toString() !== req.user._id.toString()) {
      return errorResponse(res, 'Bạn không có quyền sửa đơn đăng ký này', 403);
    }

    // Chỉ cho phép sửa khi status = pending
    if (request.status !== 'pending') {
      return errorResponse(res, 'Chỉ có thể sửa đơn đăng ký đang chờ duyệt', 400);
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return validationErrorResponse(res, errors.array());
    }

    const {
      fullName,
      phone,
      address,
      wasteTypes,
      estimatedWeight,
      preferredDate,
      preferredTime,
      notes
    } = req.body;

    // Cập nhật thông tin
    Object.assign(request, {
      fullName,
      phone,
      address,
      wasteTypes,
      estimatedWeight,
      preferredDate,
      preferredTime,
      notes
    });

    await request.save();
    await request.populate('user', 'name username email');

    successResponse(res, { request }, 'Cập nhật đơn đăng ký thành công');
  } catch (error) {
    console.error('Update request error:', error);
    if (error.name === 'CastError') {
      return notFoundResponse(res, 'ID đơn đăng ký không hợp lệ');
    }
    errorResponse(res, 'Lỗi server khi cập nhật đơn đăng ký');
  }
};

// @desc    Hủy đơn đăng ký
// @route   DELETE /api/waste-collection/:id
// @access  Private (User - chỉ đơn của mình)
const cancelRequest = async (req, res) => {
  try {
    const request = await WasteCollectionRequest.findById(req.params.id);

    if (!request) {
      return notFoundResponse(res, 'Không tìm thấy đơn đăng ký');
    }

    // Kiểm tra quyền sở hữu
    if (request.user.toString() !== req.user._id.toString()) {
      return errorResponse(res, 'Bạn không có quyền hủy đơn đăng ký này', 403);
    }

    // Chỉ cho phép hủy khi status = pending hoặc approved
    if (!['pending', 'approved'].includes(request.status)) {
      return errorResponse(res, 'Không thể hủy đơn đăng ký này', 400);
    }

    request.status = 'cancelled';
    await request.save();

    successResponse(res, { request }, 'Hủy đơn đăng ký thành công');
  } catch (error) {
    console.error('Cancel request error:', error);
    if (error.name === 'CastError') {
      return notFoundResponse(res, 'ID đơn đăng ký không hợp lệ');
    }
    errorResponse(res, 'Lỗi server khi hủy đơn đăng ký');
  }
};

// @desc    Lấy thống kê đơn đăng ký của user
// @route   GET /api/waste-collection/my-stats
// @access  Private (User)
const getMyStats = async (req, res) => {
  try {
    const stats = await WasteCollectionRequest.aggregate([
      { $match: { user: req.user._id } },
      {
        $group: {
          _id: null,
          totalRequests: { $sum: 1 },
          pendingRequests: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          approvedRequests: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          completedRequests: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          },
          rejectedRequests: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          },
          totalWeight: { $sum: '$estimatedWeight' },
          totalPointsEarned: { $sum: '$pointsEarned' }
        }
      }
    ]);

    const result = stats[0] || {
      totalRequests: 0,
      pendingRequests: 0,
      approvedRequests: 0,
      completedRequests: 0,
      rejectedRequests: 0,
      totalWeight: 0,
      totalPointsEarned: 0
    };

    successResponse(res, { stats: result }, 'Lấy thống kê thành công');
  } catch (error) {
    console.error('Get my stats error:', error);
    errorResponse(res, 'Lỗi server khi lấy thống kê');
  }
};

// ===== ADMIN FUNCTIONS =====

// @desc    Lấy tất cả đơn đăng ký (Admin only)
// @route   GET /api/waste-collection/admin/all
// @access  Private (Admin)
const getAllRequests = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const status = req.query.status;
    const search = req.query.search || '';

    const query = {};
    if (status && ['pending', 'approved', 'rejected', 'completed', 'cancelled'].includes(status)) {
      query.status = status;
    }

    // Tìm kiếm theo tên hoặc địa chỉ
    if (search) {
      query.$or = [
        { fullName: { $regex: search, $options: 'i' } },
        { address: { $regex: search, $options: 'i' } }
      ];
    }

    const requests = await WasteCollectionRequest.find(query)
      .populate('user', 'name username email')
      .populate('reviewedBy', 'name username')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await WasteCollectionRequest.countDocuments(query);

    successResponse(res, {
      requests,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }, 'Lấy danh sách đơn đăng ký thành công');
  } catch (error) {
    console.error('Get all requests error:', error);
    errorResponse(res, 'Lỗi server khi lấy danh sách đơn đăng ký');
  }
};

// @desc    Duyệt đơn đăng ký (Admin only)
// @route   PUT /api/waste-collection/admin/:id/review
// @access  Private (Admin)
const reviewRequest = async (req, res) => {
  try {
    const { status, adminNotes, actualCollectionDate, actualWeight, pointsEarned } = req.body;

    if (!['approved', 'rejected', 'completed'].includes(status)) {
      return errorResponse(res, 'Trạng thái không hợp lệ', 400);
    }

    const request = await WasteCollectionRequest.findById(req.params.id);
    if (!request) {
      return notFoundResponse(res, 'Không tìm thấy đơn đăng ký');
    }

    // Cập nhật thông tin duyệt
    request.status = status;
    request.adminNotes = adminNotes;
    request.reviewedBy = req.user._id;
    request.reviewedAt = new Date();

    if (status === 'completed') {
      request.actualCollectionDate = actualCollectionDate || new Date();
      if (actualWeight) request.actualWeight = actualWeight;
      if (pointsEarned) {
        request.pointsEarned = pointsEarned;

        // Cộng điểm cho user
        await User.findByIdAndUpdate(
          request.user,
          { $inc: { points: pointsEarned } }
        );
      }
    }

    await request.save();
    await request.populate(['user', 'reviewedBy']);

    successResponse(res, { request }, 'Duyệt đơn đăng ký thành công');
  } catch (error) {
    console.error('Review request error:', error);
    if (error.name === 'CastError') {
      return notFoundResponse(res, 'ID đơn đăng ký không hợp lệ');
    }
    errorResponse(res, 'Lỗi server khi duyệt đơn đăng ký');
  }
};

// @desc    Lấy thống kê tổng quan (Admin only)
// @route   GET /api/waste-collection/admin/stats
// @access  Private (Admin)
const getAdminStats = async (req, res) => {
  try {
    const stats = await WasteCollectionRequest.aggregate([
      {
        $group: {
          _id: null,
          totalRequests: { $sum: 1 },
          pendingRequests: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          approvedRequests: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          completedRequests: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          },
          rejectedRequests: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          },
          totalWeight: { $sum: '$actualWeight' },
          totalPointsAwarded: { $sum: '$pointsEarned' }
        }
      }
    ]);

    const result = stats[0] || {
      totalRequests: 0,
      pendingRequests: 0,
      approvedRequests: 0,
      completedRequests: 0,
      rejectedRequests: 0,
      totalWeight: 0,
      totalPointsAwarded: 0
    };

    successResponse(res, { stats: result }, 'Lấy thống kê thành công');
  } catch (error) {
    console.error('Get admin stats error:', error);
    errorResponse(res, 'Lỗi server khi lấy thống kê');
  }
};

module.exports = {
  // User functions
  createRequest,
  getMyRequests,
  getRequestById,
  updateRequest,
  cancelRequest,
  getMyStats,

  // Admin functions
  getAllRequests,
  reviewRequest,
  getAdminStats
};
