# 🎯 Hệ thống Profile với Phân quyền User/Admin

## 📋 **Tổng quan hệ thống**

### 🔐 **Phân quyền người dùng:**

#### 👤 **USER (Thành viên):**
- ✅ Đăng ký thu gom rác
- ✅ L<PERSON><PERSON> lịch sử làm quiz
- ✅ <PERSON><PERSON><PERSON> lịch sử hỏi Chat AI
- ✅ L<PERSON><PERSON> lịch sử gửi tin nhắn
- ✅ Xem thông tin cá nhân
- ✅ Đổi mật khẩu
- ✅ Đổi quà thưởng

#### 👑 **ADMIN (Quản trị viên):**
- ✅ Tất cả chức năng của User
- ✅ **Đọc và duyệt đơn đăng ký thu gom rác**
- ✅ **Đọc tin nhắn được gửi cho page**
- ✅ **Quản lý người dùng**
- ✅ **Xem thống kê hệ thống**

### 🔑 **Đ<PERSON><PERSON> nhập linh hoạt:**
- ✅ <PERSON><PERSON> thể dùng **username** hoặc **email**
- ✅ Validation thông minh
- ✅ Error messages rõ ràng

---

## 🗂️ **Cấu trúc Database Models**

### 1. **User Model** (Đã cập nhật)
```javascript
{
  name: String,           // Tên hiển thị
  username: String,       // Tên tài khoản (unique)
  email: String,          // Email (unique)
  password: String,       // Mật khẩu (hashed)
  role: ['user', 'admin'], // Vai trò
  phone: String,          // Số điện thoại
  address: String,        // Địa chỉ
  avatar: String,         // Avatar URL
  isActive: Boolean,      // Trạng thái hoạt động
  lastLogin: Date,        // Lần đăng nhập cuối
  points: Number          // Điểm thưởng
}
```

### 2. **WasteCollectionRequest Model**
```javascript
{
  user: ObjectId,         // Người đăng ký
  fullName: String,       // Họ tên
  phone: String,          // Số điện thoại
  address: String,        // Địa chỉ thu gom
  wasteTypes: [String],   // Loại rác
  estimatedWeight: Number, // Khối lượng ước tính
  preferredDate: Date,    // Ngày mong muốn
  preferredTime: String,  // Khung giờ
  status: String,         // Trạng thái
  adminNotes: String,     // Ghi chú admin
  reviewedBy: ObjectId,   // Admin duyệt
  pointsEarned: Number    // Điểm thưởng
}
```

### 3. **QuizHistory Model**
```javascript
{
  user: ObjectId,         // Người làm quiz
  quizType: String,       // Loại quiz
  questions: [Object],    // Câu hỏi và đáp án
  totalQuestions: Number, // Tổng số câu
  correctAnswers: Number, // Số câu đúng
  score: Number,          // Điểm số
  timeSpent: Number,      // Thời gian làm bài
  pointsEarned: Number    // Điểm thưởng
}
```

### 4. **ChatHistory Model**
```javascript
{
  user: ObjectId,         // Người chat
  sessionId: String,      // ID phiên chat
  messages: [Object],     // Tin nhắn
  topic: String,          // Chủ đề
  title: String,          // Tiêu đề
  totalMessages: Number,  // Tổng tin nhắn
  totalTokens: Number,    // Tổng tokens
  isActive: Boolean       // Trạng thái hoạt động
}
```

### 5. **ContactMessage Model**
```javascript
{
  user: ObjectId,         // Người gửi (optional)
  name: String,           // Họ tên
  email: String,          // Email
  subject: String,        // Chủ đề
  message: String,        // Nội dung
  category: String,       // Danh mục
  status: String,         // Trạng thái
  isRead: Boolean,        // Đã đọc chưa
  response: String,       // Phản hồi
  respondedBy: ObjectId   // Admin phản hồi
}
```

---

## 🎨 **Profile Sidebar Structure**

### **THÔNG TIN CÁ NHÂN**
- 👤 Thông tin cá nhân
- 🎁 Đổi quà
- 🔑 Đổi mật khẩu
- ⚙️ Cài đặt tài khoản

### **HOẠT ĐỘNG** (User)
- ♻️ Đăng ký thu gom rác
- ❓ Lịch sử Quiz
- 💬 Lịch sử Chat AI
- ✉️ Tin nhắn đã gửi

### **QUẢN TRỊ** (Admin only)
- ✅ Duyệt đơn thu gom `[Badge: Pending count]`
- 📥 Tin nhắn từ người dùng `[Badge: Unread count]`
- 👥 Quản lý người dùng
- 📊 Thống kê hệ thống

---

## 🚀 **API Endpoints**

### **Authentication**
- `POST /api/auth/register` - Đăng ký (với username)
- `POST /api/auth/login` - Đăng nhập (username/email)
- `GET /api/auth/me` - Thông tin user hiện tại
- `PUT /api/auth/me` - Cập nhật thông tin
- `PUT /api/auth/change-password` - Đổi mật khẩu

### **Waste Collection (User)**
- `POST /api/waste-collection` - Tạo đơn đăng ký
- `GET /api/waste-collection/my-requests` - Đơn của tôi
- `GET /api/waste-collection/:id` - Chi tiết đơn
- `PUT /api/waste-collection/:id` - Sửa đơn (pending only)
- `DELETE /api/waste-collection/:id` - Hủy đơn
- `GET /api/waste-collection/my-stats` - Thống kê cá nhân

### **Waste Collection (Admin)**
- `GET /api/waste-collection/admin/all` - Tất cả đơn
- `PUT /api/waste-collection/admin/:id/review` - Duyệt đơn
- `GET /api/waste-collection/admin/stats` - Thống kê tổng

### **Quiz History**
- `POST /api/quiz-history` - Lưu kết quả quiz
- `GET /api/quiz-history/my-history` - Lịch sử của tôi
- `GET /api/quiz-history/my-stats` - Thống kê quiz

### **Chat History**
- `POST /api/chat-history` - Lưu phiên chat
- `GET /api/chat-history/my-sessions` - Phiên chat của tôi
- `GET /api/chat-history/:sessionId` - Chi tiết phiên
- `PUT /api/chat-history/:sessionId` - Cập nhật phiên

### **Contact Messages**
- `POST /api/contact` - Gửi tin nhắn
- `GET /api/contact/my-messages` - Tin nhắn của tôi
- `GET /api/contact/admin/all` - Tất cả tin nhắn (Admin)
- `PUT /api/contact/admin/:id/respond` - Phản hồi (Admin)

---

## 🎯 **Tính năng đặc biệt**

### **Smart Caching**
- ✅ Cache thông tin user 24h
- ✅ Auto refresh sau update
- ✅ Fallback mechanism

### **Real-time Notifications**
- ✅ Badge đếm đơn chờ duyệt
- ✅ Badge đếm tin nhắn chưa đọc
- ✅ Auto update khi có thay đổi

### **Responsive Design**
- ✅ Sidebar collapse trên mobile
- ✅ Adaptive layout
- ✅ Touch-friendly interface

### **Security Features**
- ✅ Role-based access control
- ✅ JWT authentication
- ✅ Input validation
- ✅ XSS protection

---

## 📝 **Trạng thái hoàn thành**

### ✅ **Đã hoàn thành:**
- User Model với username
- Authentication với username/email
- WasteCollectionRequest Model & Controller
- QuizHistory Model
- ChatHistory Model
- ContactMessage Model
- Profile Sidebar với phân quyền
- CSS styling cho sidebar mới

### 🚧 **Đang phát triển:**
- Quiz Controller & Routes
- Chat Controller & Routes
- Contact Controller & Routes
- Admin pages
- Frontend integration

### 📋 **Kế hoạch tiếp theo:**
- Hoàn thiện tất cả Controllers
- Tạo Routes cho các endpoints
- Validation middleware
- Frontend pages cho từng chức năng
- Testing & debugging
