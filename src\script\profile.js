import apiService from './api.js';

class ProfileManager {
  constructor() {
    this.currentUser = null;
    this.init();
  }

  async init() {
    // <PERSON><PERSON><PERSON> tra authentication
    if (!apiService.isAuthenticated()) {
      window.location.href = './index.html';
      return;
    }

    this.setupEventListeners();
    // Load from cache first, then from API if needed
    await this.loadUserData(false);
  }

  setupEventListeners() {
    // Profile form
    const profileForm = document.getElementById('profileForm');
    if (profileForm) {
      profileForm.addEventListener('submit', this.handleUpdateProfile.bind(this));
    }

    // Reset profile button
    const resetProfileBtn = document.getElementById('resetProfileBtn');
    if (resetProfileBtn) {
      resetProfileBtn.addEventListener('click', this.resetProfileForm.bind(this));
    }

    // Change password form
    const changePasswordForm = document.getElementById('changePasswordForm');
    if (changePasswordForm) {
      changePasswordForm.addEventListener('submit', this.handleChangePassword.bind(this));
    }
  }

  async loadUserData(forceRefresh = false) {
    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh) {
        const cachedUser = this.getCachedUserData();
        if (cachedUser) {
          console.log('Loading user data from cache');
          this.currentUser = cachedUser;
          this.populateProfileForm();
          this.populateAccountSettings();
          return;
        }
      }

      console.log('Loading user data from API');
      const response = await apiService.getMe();
      this.currentUser = response.data.user;

      // Cache the user data
      this.cacheUserData(this.currentUser);

      this.populateProfileForm();
      this.populateAccountSettings();
    } catch (error) {
      console.error('Error loading user data:', error);
      this.showError('profileFormFeedback', 'Không thể tải thông tin người dùng');
    }
  }

  getCachedUserData() {
    try {
      const cached = localStorage.getItem('cachedUserProfile');
      if (cached) {
        const data = JSON.parse(cached);
        // Check if cache is still valid (24 hours)
        const cacheTime = new Date(data.timestamp);
        const now = new Date();
        const hoursDiff = (now - cacheTime) / (1000 * 60 * 60);

        if (hoursDiff < 24) {
          return data.user;
        } else {
          // Cache expired, remove it
          localStorage.removeItem('cachedUserProfile');
        }
      }
    } catch (error) {
      console.error('Error reading cached user data:', error);
      localStorage.removeItem('cachedUserProfile');
    }
    return null;
  }

  cacheUserData(userData) {
    try {
      const cacheData = {
        user: userData,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem('cachedUserProfile', JSON.stringify(cacheData));
    } catch (error) {
      console.error('Error caching user data:', error);
    }
  }

  clearUserCache() {
    localStorage.removeItem('cachedUserProfile');
  }

  populateProfileForm() {
    if (!this.currentUser) return;

    document.getElementById('profileName').value = this.currentUser.name || '';
    document.getElementById('profileEmail').value = this.currentUser.email || '';
    document.getElementById('profilePhone').value = this.currentUser.phone || '';
    document.getElementById('profileAddress').value = this.currentUser.address || '';
    document.getElementById('profileRole').value =
      this.currentUser.role === 'admin' ? 'Quản trị viên' : 'Thành viên';

    // Hiển thị menu admin nếu user là admin
    this.toggleAdminMenu();
  }

  toggleAdminMenu() {
    const adminElements = document.querySelectorAll('.admin-only');
    const isAdmin = this.currentUser && this.currentUser.role === 'admin';

    adminElements.forEach(element => {
      element.style.display = isAdmin ? 'block' : 'none';
    });

    // Load admin stats nếu là admin
    if (isAdmin) {
      this.loadAdminStats();
    }
  }

  async loadAdminStats() {
    try {
      // Load pending waste collection requests count
      const wasteResponse = await fetch('/api/waste-collection/admin/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (wasteResponse.ok) {
        const wasteData = await wasteResponse.json();
        const pendingCount = wasteData.data.stats.pendingRequests || 0;
        const pendingBadge = document.getElementById('pendingRequestsCount');
        if (pendingBadge) {
          pendingBadge.textContent = pendingCount;
          pendingBadge.style.display = pendingCount > 0 ? 'inline' : 'none';
        }
      }

      // Load unread messages count
      const messagesResponse = await fetch('/api/contact/admin/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (messagesResponse.ok) {
        const messagesData = await messagesResponse.json();
        const unreadCount = messagesData.data.stats.newMessages || 0;
        const unreadBadge = document.getElementById('unreadMessagesCount');
        if (unreadBadge) {
          unreadBadge.textContent = unreadCount;
          unreadBadge.style.display = unreadCount > 0 ? 'inline' : 'none';
        }
      }
    } catch (error) {
      console.error('Error loading admin stats:', error);
    }
  }

  populateAccountSettings() {
    if (!this.currentUser) return;

    document.getElementById('accountId').textContent = this.currentUser._id;
    document.getElementById('accountCreated').textContent =
      new Date(this.currentUser.createdAt).toLocaleDateString('vi-VN');
    document.getElementById('accountLastLogin').textContent =
      this.currentUser.lastLogin ?
      new Date(this.currentUser.lastLogin).toLocaleDateString('vi-VN') : 'Chưa có';

    const statusElement = document.getElementById('accountStatus');
    if (this.currentUser.isActive) {
      statusElement.textContent = 'Hoạt động';
      statusElement.className = 'badge bg-success';
    } else {
      statusElement.textContent = 'Không hoạt động';
      statusElement.className = 'badge bg-danger';
    }
  }

  async handleUpdateProfile(e) {
    e.preventDefault();

    const name = document.getElementById('profileName').value.trim();
    const phone = document.getElementById('profilePhone').value.trim();
    const address = document.getElementById('profileAddress').value.trim();
    const feedback = document.getElementById('profileFormFeedback');

    // Build update object - only include fields that have values
    const updateData = {};

    if (name) updateData.name = name;
    if (phone) updateData.phone = phone;
    if (address) updateData.address = address;

    // Check if there's anything to update
    if (Object.keys(updateData).length === 0) {
      this.showError(feedback, 'Vui lòng nhập ít nhất một thông tin để cập nhật');
      return;
    }

    try {
      this.showLoading(feedback, 'Đang cập nhật thông tin...');

      const response = await apiService.updateProfile(updateData);

      // Update current user data
      this.currentUser = response.data.user;

      // Clear cache and refresh data
      this.clearUserCache();
      this.cacheUserData(this.currentUser);

      // Repopulate form with updated data
      this.populateProfileForm();
      this.populateAccountSettings();

      this.showSuccess(feedback, response.message || 'Cập nhật thông tin thành công!');

    } catch (error) {
      this.showError(feedback, error.message);
    }
  }

  resetProfileForm() {
    this.populateProfileForm();
    document.getElementById('profileFormFeedback').innerHTML = '';
  }

  // Method to force refresh data from API
  async refreshUserData() {
    await this.loadUserData(true);
  }

  async handleChangePassword(e) {
    e.preventDefault();

    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmNewPassword = document.getElementById('confirmNewPassword').value;
    const feedback = document.getElementById('changePasswordFormFeedback');

    // Validate password confirmation
    if (newPassword !== confirmNewPassword) {
      this.showError(feedback, 'Mật khẩu mới và xác nhận mật khẩu không khớp');
      return;
    }

    // Validate password strength
    if (!this.validatePassword(newPassword)) {
      this.showError(feedback, 'Mật khẩu mới phải có ít nhất 6 ký tự, bao gồm chữ hoa, chữ thường và số');
      return;
    }

    try {
      this.showLoading(feedback, 'Đang đổi mật khẩu...');

      const response = await apiService.changePassword({
        currentPassword,
        newPassword,
        confirmPassword: confirmNewPassword
      });

      this.showSuccess(feedback, response.message);

      // Reset form
      document.getElementById('changePasswordForm').reset();

    } catch (error) {
      this.showError(feedback, error.message);
    }
  }

  validatePassword(password) {
    // At least 6 characters, contains uppercase, lowercase, and number
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,}$/;
    return regex.test(password);
  }



  // Helper methods
  showLoading(element, message) {
    element.innerHTML = `
      <div class="alert alert-info d-flex align-items-center" role="alert">
        <div class="spinner-border spinner-border-sm me-2" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        ${message}
      </div>
    `;
  }

  showSuccess(element, message) {
    element.innerHTML = `
      <div class="alert alert-success" role="alert">
        <i class="bi bi-check-circle me-2"></i>${message}
      </div>
    `;
  }

  showError(element, message) {
    element.innerHTML = `
      <div class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>${message}
      </div>
    `;
  }
}

// Sidebar toggle function
function toggleSidebar() {
  const sidebar = document.getElementById('profileSidebar');
  const content = document.getElementById('profileContent');

  if (sidebar && content) {
    sidebar.classList.toggle('collapsed');
    content.classList.toggle('expanded');
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ProfileManager();

  // Make toggleSidebar available globally
  window.toggleSidebar = toggleSidebar;
});
